{"name": "aternos-bedrock-bot", "version": "1.0.0", "description": "24/7 Keep-<PERSON> <PERSON><PERSON> for Aternos Bedrock Server", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "node index.js", "build": "echo 'No build step required'", "postinstall": "echo 'Installation complete'", "setup-betterstack": "node setup-betterstack.js", "crack": "node -e \"const config = require('./config'); config.useCrackServerPreset(); console.log('✅ Crack mode enabled');\"", "premium": "node -e \"const config = require('./config'); config.usePremiumServerPreset(); console.log('✅ Premium mode enabled');\""}, "dependencies": {"bedrock-protocol": "^3.45.0", "express": "^4.18.2", "axios": "^1.6.0", "node-cron": "^3.0.3", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}, "keywords": ["minecraft", "bedrock", "bot", "keepalive", "aternos", "automation"], "author": "<PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "your-repo-url"}}